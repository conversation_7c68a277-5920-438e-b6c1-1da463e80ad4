/*
 * Xgm00301Bean.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.enterprise.context.SessionScoped;
import javax.faces.model.SelectItem;
import javax.inject.Inject;
import javax.inject.Named;

import com.jast.gakuen.core.common.SystemInfo;
import com.jast.gakuen.core.common.constant.code.PrdKbn;
import com.jast.gakuen.core.common.dto.OptionDTO;
import com.jast.gakuen.core.common.exception.DataException;
import com.jast.gakuen.core.common.service.IOptionService;
import com.jast.gakuen.core.common.util.Message;
import com.jast.gakuen.core.common.util.UtilDate;
import com.jast.gakuen.core.common.util.UtilFaces;
import com.jast.gakuen.core.gk.GkBaseBean;
import com.jast.gakuen.core.gk.annotation.GkBackingBean;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO01;
import com.jast.gakuen.gk.xg.service.IXgm003Service;

import lombok.Getter;
import lombok.Setter;

/**
 * 学生納付金通知書出力
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Named
@SessionScoped
public class Xgm00301Bean extends GkBaseBean {

	@Getter
	@Setter
	protected Xgm003ConditionDTO01 condition = new Xgm003ConditionDTO01();

	/**
	 * 学生納付金通知書ービス
	 */
	@Inject
	protected IXgm003Service xgm003Service;

	/**
	 * オプションサービス
	 */
	@Inject
	protected IOptionService optionService;

	/**
	 * 納付金指定タブ
	 */
	@Inject
	@Getter
	protected Xgm00301T01Bean xgm00301T01Bean;

	/**
	 * 納付金・学生指定タブ
	 */
	@Inject
	@Getter
	protected Xgm00301T02Bean xgm00301T02Bean;

	/**
	 * 学生指定タブ
	 */
	@Inject
	@Getter
	protected Xgm00301T03Bean xgm00301T03Bean;

	/**
	 * 一覧のid
	 */
	@Getter
	protected final String[] tblId = { "t1_payList", "t2_payList" };

	/** 一覧の表示件数 */
	@Getter
	@Setter
	protected Integer[] tblRows = new Integer[2];

	/** 一覧の表示ページ */
	@Getter
	@Setter
	protected Integer[] tblFirst = new Integer[2];

	/**
	 * 並び順指定コンボリスト
	 */
	@Getter
	protected List<SelectItem> orderItems = new ArrayList<>();



	/**
	 * 初期表示処理
	 *
	 * @throws Exception 例外
	 */
	@Override
	protected void doInit() throws Exception {
		// ------------------------------
		// 初期値設定
		// ------------------------------
		// 発行日（hattyuDate）のデフォルト値を現在日付に設定（未設定の場合のみ）
		if (this.condition.getHattyuDate() == null) {
			this.condition.setHattyuDate(SystemInfo.getSystemDate());
		}
		
		// ------------------------------
		// オプション情報を取得
		// ------------------------------
		OptionDTO option = new OptionDTO();
		option.setFormId(UtilFaces.getFormId());
		option.setBaseDto(this.condition);
		this.optionService.get(option);
	}

	/**
	 * 通信欄の活性制御
	 * 通信区分チェックありの場合：活性、チェックなしの場合：非活性
	 *
	 * @return 通信欄の活性制御フラグ
	 */
	@GkBackingBean
	public boolean getTsuukyakDisabled() {
		// 通信区分がチェックされていない場合は非活性
		return !this.condition.isTsuukyakKbn();
	}

	/**
	 * 納入期限の活性制御
	 * 期限区分チェックありの場合：活性、チェックなしの場合：非活性
	 *
	 * @return 納入期限の活性制御フラグ
	 */
	@GkBackingBean
	public boolean getPayLimitDisabled() {
		// 期限区分がチェックされていない場合は非活性
		return !this.condition.isLimitKbn();
	}

	/**
	 * 有効期限の活性制御
	 * 期限区分チェックありの場合：活性、チェックなしの場合：非活性
	 *
	 * @return 有効期限の活性制御フラグ
	 */
	@GkBackingBean
	public boolean getYukouLimitDisabled() {
		// 期限区分がチェックされていない場合は非活性
		return !this.condition.isLimitKbn();
	}

	/**
	 * オプション情報に保存
	 *
	 * @throws Exception 例外
	 */
	protected void saveOption() throws Exception {
		// オプション情報を保存
		OptionDTO option = new OptionDTO();
		option.setFormId(UtilFaces.getFormId());
		option.setBaseDto(this.condition);
		this.optionService.set(option);
	}

	/**
	 * 発行ボタン押下時チェック
	 *
	 */
	public void checkProc() throws Exception {
		// 【相関チェック】
		// 通信区分がチェックされている且つ、通信欄が96桁、7行以上の場合、エラー(E_SY_00036)
		if (this.condition.isTsuukyakKbn() && this.condition.getTsuukyak().length() > 96 * 7) {
			throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 36, "通信欄は１行１６文字、６行以内"));
		}
		// 期限区分がチェック有りの場合、納入期限・有効期限は空欄不可。(E_SY_00046)
		if (this.condition.isLimitKbn() && (this.condition.getPayLimit() == null || this.condition.getYukouLimit() == null)) {
			throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 46, "期限を直接入力する際は、納入期限/有効期限"));
		}
		// 期限区分がチェック有りの場合、納入期限＞有効期限の場合エラー(E_SY_00050)
		if (this.condition.isLimitKbn() && this.condition.getPayLimit() != null && this.condition.getYukouLimit() != null) {
			if (this.condition.getPayLimit().after(this.condition.getYukouLimit())) {
				throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 50, "有効期限より後の納入期限"));
			}
		}
		// 納入期限＋12(※)ヶ月≦有効期限となっているかチェックする。(E_SY_00219)
		// ※『EUC設定ファイル一覧』の種別名称が、「学費の納入期限範囲」を参照。
		if (this.condition.isLimitKbn() && this.condition.getPayLimit() != null && this.condition.getYukouLimit() != null) {
			int mth = xgm003Service.getGakuhiNoufuKigenRange();
			Date payLimitDate = UtilDate.calcDate(this.condition.getPayLimit(), 0, mth, 0);
			if (payLimitDate.after(this.condition.getYukouLimit())) {
				throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 219, "納入期限＋" + mth + "ヶ月", "有効期限"));
			}
		}
	}

	@GkBackingBean
	public void doSearchPayHList(final int tubNo) throws Exception {
		if (tubNo == 1) {
			xgm00301T01Bean.doSearch();
		} else if (tubNo == 2) {
			xgm00301T02Bean.doSearch();
		}
	}

	@GkBackingBean
	public void doSearchPayWList() throws Exception {
		xgm00301T03Bean.doSearch();
	}

	/**
	 * 「コード：名称」作成
	 *
	 * @param code コード値
	 * @param name 名称値
	 * @return 「コード：名称」値
	 * @throws Exception 例外
	 */
	public String doJoinCodeName(final Integer code, final String name) throws Exception {
		// Integer を String に変換して処理
		String codeStr = (code != null) ? code.toString() : null;
		return doJoinCodeName(codeStr, name);
	}

	/**
	 * 「コード：名称」作成
	 *
	 * @param code コード値
	 * @param name 名称値
	 * @return 「コード：名称」値
	 * @throws Exception 例外
	 */
	public String doJoinCodeName(final String code, final String name) throws Exception {
		// 返却用文字列変数
		String rtnStr = "";
		// コード値がNULLではない場合
		if (code != null && !code.isEmpty()) {
			// コード値設定
			rtnStr = code;
			// 名称がNULLではない場合
			if (name != null && !name.isEmpty()) {
				// コロンと名称値を設定
				rtnStr = rtnStr + ":" + name;
			}
		}
		return rtnStr;
	}

}
