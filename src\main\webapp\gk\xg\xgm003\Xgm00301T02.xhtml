<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                xmlns:rx="http://xmlns.jcp.org/jsf/composite/component">

    <section>

        <p:outputPanel id="t2_paySearchCondition">
            <dl class="inputArea">
                <!-- 割当年度 -->
                <div class="itemTable">
                    <dt class="hissu">
                        <p:outputLabel for="funcForm:tabArea:t2_wariateNendo:number" value="#{item['xgm003.wariateNendo.0.label']}" />
                        <rx:gkSizeLabel />
                    </dt>
                    <dd>
                        <rx:inputNumber id="t2_wariateNendo" widgetVar="nendo" value="#{xgm00301T02Bean.condition.wariateNendo}" styleClass="inputHalfSize4" >
                            <p:clientValidator event="blur" />
                        </rx:inputNumber>
                        <p:message for="funcForm:tabArea:t2_wariateNendo:number" display="tooltip" />
                    </dd>
                </div>
                <!--納付金種別-->
                <div class="itemTable">
                    <dt>
                        <p:outputLabel value="#{item['xgm003.noufuKinShubetsu.0.label']}" />
                    </dt>
                    <dd>
                        <p:selectManyCheckbox id="t2_noufuKinShubetsu" value="#{xgm00301T02Bean.condition.noufuKinShubetsu}">
                            <f:selectItems value="#{noufuKinShubetsu.itemValues}" var="itemVal"
                                           itemValue="#{itemVal}" itemLabel="#{noufuKinShubetsu.getShortLabel(itemVal)}" />
                        </p:selectManyCheckbox>
                    </dd>
                </div>
                <!-- 業務コード -->
                <div class="itemTable">
                    <dt>
                        <p:outputLabel for="t2_gyomucd" value="#{item['xgm003.gyomucd.0.label']}" />
                        <rx:gkSizeLabel />
                    </dt>
                    <dd>
                        <p:selectOneMenu id="t2_gyomucd" value="#{xgm00301T02Bean.condition.gyomucd}">
                            <f:selectItems value="#{ghzSelectItem.getGyoumuItems('M01', 'M')}" />
                        </p:selectOneMenu>
                        <p:message for="t2_gyomucd" display="tooltip" />
                    </dd>
                </div>
            </dl>
        </p:outputPanel>
            
        <!-- ボタンエリア -->
        <p:outputPanel styleClass="btnAreaFuncBottom" >
            <!-- 検索 -->
            <p:commandButton id="t2_paySearch" value="#{btn['button.001']}" actionListener="#{ghd00802Bean.doSearchPayHList(3)}"
                             process="@this funcForm:mainCondition funcForm:outputKbnCondition t2_paySearchCondition"
                             update="@this t2_paySearchCondition funcForm:tabArea:t2_payList funcForm:tabArea:t2_paySearch funcForm:tabArea:t2_payClear"
                             styleClass="btnSearch letterSpaceHalf" />
            <!-- クリア -->
            <p:commandButton id="t2_payClear" value="#{btn['button.008']}" actionListener="#{xgm00301T02Bean.doClear}"
                             process="@this"
                             update="@this funcForm:tabArea:t2_paySearchCondition funcForm:tabArea:t2_payList funcForm:tabArea:t2_paySearch funcForm:tabArea:t2_payClear"
                             styleClass="btnClear"/>
        </p:outputPanel>
            
        <p:outputPanel id="t2_mainCondition">
            <dl>
                <div>
                    <!-- 納付金リスト -->
                    <p:dataTable id="#{ghd00802Bean.tblId[1]}"
                                 rows="#{ghd00802Bean.tblRows[1]}"
                                 first="#{ghd00802Bean.tblFirst[1]}"
                                 var="data" value="#{xgm00301T02Bean.payList}"
                                 selection="#{xgm00301T02Bean.selectedPayList}" rowKey="#{data.hashCode()}"
                                 widgetVar="dataTable" paginator="true" resizableColumns="false" scrollHeight="140"
                                 emptyMessage="#{xgm00301T02Bean.searched?msgSY['E_SY_00090']:''}">

                        <!--ページャ保存-->
                        <p:ajax event="page" listener="#{ghd00802Bean.onPage}" />

                        <!-- チェックボックス -->
                        <p:column selectionMode="multiple" class="colSizeCheckbox alignCenter" />

                        <!-- 割当年度 -->
                        <p:column id="t2_colNendo" headerText="#{item['ghePaywBun.nendo.0.label']}"
                                  sortBy="#{data.nendo}" styleClass="colSize6 alignLeft">
                            <h:outputText value="#{data.nendo}" />
                        </p:column>

                        <!-- 納付金コード -->
                        <p:column id="t2_colpayCd" headerText="#{item['ghePaywBun.payCd.0.label']}"
                                  sortBy="#{data.payCd}" styleClass="colSize8 alignLeft">
                            <h:outputText value="#{data.payCd}" />
                        </p:column>

                        <!-- パターンコード -->
                        <p:column id="t2_colPatternCd" headerText="#{item['ghePaywBun.patternCd.0.shortLabel']}"
                                  sortBy="#{data.patternCd}"
                                  styleClass="colSize9 alignLeft">
                            <h:outputText value="#{data.patternCd}" />
                        </p:column>

                        <!-- 分納区分コード -->
                        <p:column id="t2_colBunnoKbnCd" headerText="#{item['ghePaywBun.bunnoKbnCd.0.shortLabel']}"
                                  sortBy="#{data.bunnoKbnCd}"
                                  styleClass="colSize9 alignLeft">
                            <h:outputText value="#{ghd00802Bean.doJoinCodeName(data.bunnoKbnCd,data.bunnoKbnName)}" />
                        </p:column>

                        <!-- 納付金名称 -->
                        <p:column id="t2_colPayName" headerText="#{item['ghcPayh.payName.0.label']}"
                                  sortBy="#{data.payName}"
                                  styleClass="colSizeInitial alignLeft">
                            <h:outputText value="#{data.payName}" />
                        </p:column>

                        <!-- 分割NO -->
                        <p:column id="t2_colBunkatsuNo" headerText="#{item['ghePaywBun.bunkatsuNo.0.label']}"
                                  sortBy="#{data.bunkatsuNo}"
                                  styleClass="colSize6 alignRight">
                            <h:outputText value="#{data.bunkatsuNo}" />
                        </p:column>
                    </p:dataTable>
                </div>
            </dl>
            
            <!-- 学生個別指定エリア -->
            <rx:ghzIndvDesignationGaksei value="#{xgm00301T02Bean.targetGakseiList}" scrollHeight="280" checkDuplicate="true" businessFunc="#{xgm00301T02Bean.businessFunc}" sKijunDate="#{xgm00301T02Bean.conditionOutput.skijunDate}" checkKanriGai="true"/>
        </p:outputPanel>
    </section>

    <!--メインボタン-->
    <div class="btnAreaFuncBottom">
        <!--発行する-->
        <rx:asyncExecRequest id="t2_outputPrint" compoAsyncId="outputPrintBtn2"
                             process="@this funcForm:mainCondition funcForm:tabArea:t2_mainCondition @(.gkOrdDesignation)"
                             asyncExecReqDto="#{xgm00301T02Bean.collectiveOutputReport}"
                             update="@this funcForm:mainCondition funcForm:tabArea:t2_mainCondition @(.gkOrdDesignation)"
                             beforeMethod="#{xgm00301Bean.doOutputPrint(2)}"
                             buttonName="#{btn['button.802']}" buttonStyleClass="btnFileoutput letterSpaceHalf" />
    </div>
</ui:composition>